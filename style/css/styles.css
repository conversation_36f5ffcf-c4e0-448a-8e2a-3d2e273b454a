/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Arial', sans-serif;
  }
  
  body {
    background-image: url('../images/RC_Blur.png');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    color: #333;
    line-height: 1.6;
  }
  
  .header {
    background-color: rgba(32, 64, 96, 0.9);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  }
  
  .container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
  }
  
  .logout {
    text-align: right;
    color: #fff;
  }
  
  .logout a {
    color: #fff;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
  }
  
  .logout a:hover {
    color: #ffd700;
    text-decoration: underline;
  }
  
  .middlecontent {
    padding: 2rem 0;
    min-height: calc(100vh - 5rem);
  }
  
  .whitesec {
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }
  
  .inner-padding {
    padding: 2rem;
  }
  
  .frm-head {
    margin-bottom: 2rem;
    text-align: center;
  }
  
  .frm-head h1 {
    color: #204060;
    font-size: 2rem;
    position: relative;
    padding-bottom: 1rem;
  }
  
  .frm-head h1:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background: linear-gradient(to right, #204060, #4c86b0);
  }
  
  /* Admin Menu Styles */
  .admin-menu {
    margin-top: 1rem;
  }
  
  .menu-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
  }
  
  .menu-btn {
    display: block;
    padding: 1.2rem;
    text-align: center;
    background-color: #204060;
    color: #fff;
    font-weight: 600;
    text-decoration: none;
    border-radius: 6px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }
  
  .menu-btn:hover {
    background-color: #4c86b0;
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
  }
  
  /* Form Styles */
  .row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
  }
  
  .form-group {
    padding: 0 15px;
    margin-bottom: 1.5rem;
    width: 100%;
  }
  
  .form-control {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  }
  
  .form-control:focus {
    border-color: #4c86b0;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(32, 64, 96, 0.25);
  }
  
  label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #204060;
  }
  
  textarea.form-control {
    resize: vertical;
  }
  
  .form-submit {
    margin-top: 1rem;
  }
  
  .btn {
    display: inline-block;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    text-decoration: none;
    color: white;
    background-color: #204060;
    border: none;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
  }
  
  .btn:hover {
    background-color: #4c86b0;
    transform: translateY(-2px);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
  }
  
/* Table styles */
.table-responsive {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.table {
  min-width: 1000px; /* Adjust based on your column count */
  width: 100%;
  overflow-x: auto;
}

  
  table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
    background-color: #fff;
  }
  
  th, td {
    padding: 0.75rem;
    text-align: center;
    border: 1px solid #dee2e6;
  }
  
  th {
    background-color: #204060;
    color: white;
    font-weight: 600;
    position: sticky;
    top: 0;
  }
  
  tbody tr:nth-child(odd) {
    background-color: #f8f9fa;
  }
  
  tbody tr:hover {
    background-color: #e9ecef;
  }
  
  /* Animation */
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
  }
  
  .whitesec {
    animation: fadeIn 0.5s ease-out;
  }
  
  /* Responsive styles */
  @media (max-width: 992px) {
    .inner-padding {
      padding: 1.5rem;
    }
    
    .frm-head h1 {
      font-size: 1.75rem;
    }
    
    .menu-grid {
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
  }
  
  @media (max-width: 768px) {
    .inner-padding {
      padding: 1rem;
    }
    
    .frm-head h1 {
      font-size: 1.5rem;
    }
    
    .menu-grid {
      grid-template-columns: 1fr;
    }
  }
  
  @media (max-width: 576px) {
    .container {
      width: 95%;
    }
    
    .logout {
      text-align: center;
      font-size: 0.9rem;
    }
    
    .frm-head h1 {
      font-size: 1.25rem;
    }
    
    .menu-btn {
      padding: 1rem;
    }
  }

  .table-header-filter {
  position: relative;
  display: inline-block;
}

.table-header-filter .dropdown-btn {
  background-color: #f2f2f2;
  border: 1px solid #ccc;
  padding: 2px 5px;
  cursor: pointer;
  font-size: 12px;
  margin-top: 5px;
}

.table-header-filter .dropdown-content {
  display: none;
  position: absolute;
  background-color: #fff;
  min-width: 180px;
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #ddd;
  z-index: 999;
  padding: 10px;
  box-shadow: 0px 8px 16px rgba(0,0,0,0.1);
}

.table-header-filter.show .dropdown-content {
  display: block;
}

.table-header-filter .dropdown-content label {
  display: block;
  margin: 3px 0;
  font-size: 13px;
}
