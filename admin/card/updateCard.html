<script src="/style/js/roleCheck.js"></script>
<script>
  checkRoleAccess(['officeAdmin', 'cardAdmin', 'superAdmin']); // customize allowed roles per page
</script>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Update Card Details</title>

  <!-- Load jQuery first -->
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.2/jquery.min.js"></script>

  <!-- Styles and Scripts -->
  <script src="../../style/js/plugin.js"></script>
  <script src="../../style/js/bootstrap-datepicker.min.js"></script>
  <script src="../../style/js/clockpicker.js"></script>
  <script src="../../style/js/custom.js"></script>
  <script src="../../style/js/config.js"></script>
  <script src="/sessionstorage.js"></script>
  <script src="updateCard.js" defer></script>

  <link rel="stylesheet" href="../../style/css/styles.css" />
</head>
<body>
  <div class="header">
    <div class="container">
      <div class="logout">
        <a href="javascript:void(0);" onclick="history.back()">Back</a>
        &nbsp; | &nbsp; <a href="javascript:void(0);" onclick="goToHome()">Home</a> &nbsp; | &nbsp;
        <a href="javascript:void(0);" onclick="logout()">Logout</a>
      </div>
    </div>
  </div>

  <div class="middlecontent">
    <div class="container">
      <div class="whitesec">
        <div class="inner-padding">
          <div class="frm-head">
            <h1>Update Card Details</h1>
          </div>

          <div class="form">
            <form id="updateForm">
              <div class="form-group">
                <label for="cardno">Card Number:</label>
                <input type="text" id="cardno" name="cardno" class="form-control" readonly />
              </div>

              <div class="form-group">
                <label for="issuedto">Issued To:</label>
                <input type="text" id="issuedto" name="issuedto" class="form-control" required />
              </div>

              <div class="form-group">
                <label for="gender">Gender:</label>
                <input type="text" id="gender" name="gender" class="form-control" required />
              </div>

              <div class="form-group">
                <label for="dob">Date of Birth:</label>
                <input type="date" id="dob" name="dob" class="form-control" required />
              </div>

              <div class="form-group">
                <label for="mobno">Mobile Number:</label>
                <input type="tel" id="mobno" name="mobno" class="form-control" required />
              </div>

              <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" name="email" class="form-control" required />
              </div>

              <div class="form-group">
                <label for="idType">ID Type:</label>
                <input type="text" id="idType" name="idType" class="form-control" required />
              </div>

              <div class="form-group">
                <label for="idNo">ID Number:</label>
                <input type="text" id="idNo" name="idNo" class="form-control" required />
              </div>

              <div class="form-group">
                <label for="address">Address:</label>
                <textarea id="address" name="address" rows="4" class="form-control" required></textarea>
              </div>

              <div class="form-group">
                <label for="state">State:</label>
                <select id="state" name="state" class="form-control" required></select>
              </div>

              <div class="form-group">
                <label for="city">City:</label>
                <select id="city" name="city" class="form-control" required></select>
              </div>

              <div class="form-group">
                <label for="pin">Pin Code:</label>
                <input type="text" id="pin" name="pin" class="form-control" required />
              </div>

              <div class="form-group">
  <label for="centre">Centre:</label>
  <select id="center" name="centre" class="form-control" required>
    <option value="">Select Centre</option>
    <option value="Ahmedabad">Ahmedabad</option>
    <option value="Anand">Anand</option>
    <option value="Andheri">Andheri</option>
    <option value="Bangalore">Bangalore</option>
    <option value="Bhandup">Bhandup</option>
    <option value="Bhayandar">Bhayandar</option>
    <option value="Borivali">Borivali</option>
    <option value="Canada">Canada</option>
    <option value="Chembur">Chembur</option>
    <option value="Chennai">Chennai</option>
    <option value="Coimbatore">Coimbatore</option>
    <option value="Dahisar">Dahisar</option>
    <option value="Delhi">Delhi</option>
    <option value="Dhule">Dhule</option>
    <option value="Dombivali">Dombivali</option>
    <option value="Dubai">Dubai</option>
    <option value="Ghatkopar">Ghatkopar</option>
    <option value="Hubli">Hubli</option>
    <option value="Hyderabad">Hyderabad</option>
    <option value="Jabalpur">Jabalpur</option>
    <option value="Jamshedpur">Jamshedpur</option>
    <option value="Kandivali">Kandivali</option>
    <option value="Khar">Khar</option>
    <option value="Kolkata">Kolkata</option>
    <option value="Kuwait">Kuwait</option>
    <option value="Mahim">Mahim</option>
    <option value="Malad">Malad</option>
    <option value="Matunga">Matunga</option>
    <option value="Mulund">Mulund</option>
    <option value="Nagpur">Nagpur</option>
    <option value="Nagri">Nagri</option>
    <option value="New Mumbai">New Mumbai</option>
    <option value="Pondichhery">Pondichhery</option>
    <option value="Pune">Pune</option>
    <option value="Raipur">Raipur</option>
    <option value="Raj Nagar">Raj Nagar</option>
    <option value="Rajim">Rajim</option>
    <option value="Rajkot">Rajkot</option>
    <option value="Rajnandgaon">Rajnandgaon</option>
    <option value="Ranchi">Ranchi</option>
    <option value="Santacruz">Santacruz</option>
    <option value="South Mumbai">South Mumbai</option>
    <option value="Thane">Thane</option>
    <option value="UAE">UAE</option>
    <option value="USA East Coast">USA East Coast</option>
    <option value="USA West Coast">USA West Coast</option>
    <option value="Vile Parla">Vile Parla</option>
    <option value="Wadala">Wadala</option>
    <option value="Others">Others</option>
  </select>
</div>

              <div class="form-group">
                <label for="res_status">Residential Status:</label>
                <select id="res_status" name="res_status" class="form-control" required>
                  <option value="">Select Status</option>
                  <option value="MUMUKSHU">MUMUKSHU</option>
                  <option value="PR">PR</option>
                  <option value="SEVA KUTIR">SEVA KUTIR</option>
                  <option value="GUEST">GUEST</option>
                </select>
              </div>

              <!-- Add this inside <form id="updateForm">, after Residential Status -->
<div class="form-group guest-only" style="display: none;">
  <label for="referenceCardno">Reference Card Number:</label>
  <input type="text" id="referenceCardno" name="referenceCardno" class="form-control" />
</div>

<!-- <div class="form-group guest-only" style="display: none;">
  <label for="guestType">Guest Type:</label>
  <select id="guestType" name="guestType" class="form-control">
    <option value="">Select Type</option>
    <option value="Family">Family</option>
    <option value="Friend">Friend</option>
    <option value="Driver">Driver</option>
                        <option value="VIP">VIP</option>
                      </select>
</div>
 -->
              <div class="form-group form-submit">
                <button type="submit" class="btn btn-primary">Save</button>
              </div>
            </form>
          </div>

        </div>
      </div>
    </div>
  </div>
</body>
</html>
