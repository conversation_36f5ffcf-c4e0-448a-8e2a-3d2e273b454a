<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>PR Residents Report</title>

    <!-- jQuery -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.2/jquery.min.js"></script>

    <!-- Additional Scripts -->
    <script src="../../style/js/plugin.js"></script>
    <script src="../../style/js/bootstrap-datepicker.min.js"></script>
    <script src="../../style/js/clockpicker.js"></script>
    <script src="../../style/js/custom.js"></script>
    <script src="../../style/js/config.js"></script>
    <script src="../../sessionstorage.js"></script>
    <script src="totalPR.js" defer></script>
    <script src="../../style/js/tableSortFilter.js" defer></script>

    <!-- Shared Styles -->
    <link rel="stylesheet" href="../../style/css/styles.css" />
  </head>

  <body>
    <script src="/style/js/roleCheck.js"></script>
    <script>
      checkRoleAccess(['gateAdmin', 'superAdmin']);
    </script>

    <div class="header">
      <div class="container">
        <div class="logout">
          <a href="javascript:void(0);" onclick="history.back()">Back</a>
          &nbsp; | &nbsp;
          <a href="javascript:void(0);" onclick="goToHome()">Home</a>
          &nbsp; | &nbsp;
          <a href="javascript:void(0);" onclick="logout()">Logout</a>
        </div>
      </div>
    </div>

    <div class="middlecontent">
      <div class="container">
        <div class="whitesec">
          <div class="inner-padding">
            <div class="frm-head">
              <h1>PR Residents Report</h1>
            </div>

            <div class="form">
              <input
                type="text"
                id="tableSearch"
                class="form-control search-box"
                placeholder="Search..."
              />

              <div class="table-responsive">
                <table
                  id="prResidentsTable"
                  class="table table-striped table-bordered"
                >
                  <thead>
                    <tr>
                      <th>#</th>
                      <th>Card No</th>
                      <th>Issued To</th>
                      <th>Mobile No</th>
                      <th>Last Gate in time</th>
                      <th>Last Gate out time</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody id="prResidents">
                    <!-- Data populated by JavaScript -->
                  </tbody>
                </table>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>

    <!-- Gate History Modal -->
<div
  id="gateHistoryModal"
  class="modal"
  style="
    display: none;
    position: fixed;
    top: 10%;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    background: #fff;
    border: 1px solid #ccc;
    padding: 20px;
    max-width: 600px;
    width: 90%;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  "
>
  <h3 id="gateHistoryTitle">Gate History</h3>

  <div style="max-height: 300px; overflow-y: auto;">
    <table class="table table-bordered" style="margin-bottom: 15px;">
      <thead>
        <tr>
          <th>Status</th>
          <th>Time</th>
          <th>Updated By</th>
        </tr>
      </thead>
      <tbody id="gateHistoryBody">
        <!-- Fetched rows will go here -->
      </tbody>
    </table>
  </div>

  <div style="text-align: right;">
    <button onclick="document.getElementById('gateHistoryModal').style.display='none'">Close</button>
  </div>
</div>
 </body>
</html>
