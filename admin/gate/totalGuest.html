<script src="/style/js/roleCheck.js"></script>
<script>
  checkRoleAccess(['gateAdmin', 'superAdmin']);
</script>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Guests Report</title>

  <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.2/jquery.min.js"></script>
  <script src="../../style/js/plugin.js"></script>
  <script src="../../style/js/bootstrap-datepicker.min.js"></script>
  <script src="../../style/js/clockpicker.js"></script>
  <script src="../../style/js/custom.js"></script>
  <script src="../../style/js/config.js"></script>
  <script src="/sessionstorage.js"></script>
  <script src="totalGuest.js"></script>
  <script src="../../style/js/tableSortFilter.js" defer></script>

  <link rel="stylesheet" href="../../style/css/styles.css" />
</head>

<body>
  <div class="header">
    <div class="container">
      <div class="logout">
        <a href="javascript:void(0);" onclick="history.back()">Back</a>
        &nbsp; | &nbsp;
        <a href="javascript:void(0);" onclick="goToHome()">Home</a>
        &nbsp; | &nbsp;
        <a href="javascript:void(0);" onclick="logout()">Logout</a>
      </div>
    </div>
  </div>

  <div class="middlecontent">
    <div class="container">
      <div class="whitesec">
        <div class="inner-padding">
          <div class="frm-head">
            <h1>Guests Report</h1>
          </div>

          <div class="form">
            <input type="text" id="tableSearch" class="form-control search-box" placeholder="Search...">
            <div class="table-responsive">
              <table id="prResidentsTable" class="table table-striped table-bordered">
                <thead>
                  <tr>
                    <th>#</th>
                    <th>Card No</th>
                    <th>Issued To</th>
                    <th>Mobile No</th>
                    <th>Last Gate in time</th>
                    <th>Last Gate out time</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody id="prResidents"></tbody>
              </table>
            </div>
          </div>

          <!-- Gate History Modal -->
          <div id="historyModal" class="custom-modal">
            <div class="custom-modal-content">
              <h3 id="historyTitle"></h3>
              <div class="table-responsive">
                <table class="table table-bordered">
                  <thead>
                    <tr>
                      <th>Status</th>
                      <th>Time</th>
                      <th>Updated By</th>
                    </tr>
                  </thead>
                  <tbody id="historyTableBody"></tbody>
                </table>
              </div>
              <button onclick="closeModal()">Close</button>
            </div>
          </div>

          <style>
            .custom-modal {
              display: none;
              position: fixed;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              background: rgba(0, 0, 0, 0.5);
              z-index: 9999;
            }

            .custom-modal-content {
              background: #fff;
              padding: 20px;
              margin: 5% auto;
              width: 80%;
              max-height: 80%;
              overflow-y: auto;
              border-radius: 8px;
              position: relative;
            }

            .custom-modal table th, .custom-modal table td {
              white-space: nowrap;
            }
          </style>
        </div>
      </div>
    </div>
  </div>

  <script>
    function openHistory(cardno) {
      fetch(`${CONFIG.basePath}/gate/history/${cardno}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${sessionStorage.getItem('token')}`
        }
      })
      .then(response => response.json())
      .then(data => {
        const historyTableBody = document.getElementById('historyTableBody');
        const modal = document.getElementById('historyModal');
        const title = document.getElementById('historyTitle');
        historyTableBody.innerHTML = '';
        title.textContent = `Gate History for Card No: ${cardno}`;

        if (data.data && data.data.length > 0) {
          data.data.forEach(entry => {
            const row = document.createElement('tr');
            row.innerHTML = `
              <td>${entry.status}</td>
              <td>${formatDateTime(entry.createdAt)}</td>
              <td>${entry.updatedBy || '-'}</td>
            `;
            historyTableBody.appendChild(row);
          });
        } else {
          historyTableBody.innerHTML = `<tr><td colspan="3">No records found</td></tr>`;
        }

        modal.style.display = 'block';
      })
      .catch(err => {
        alert('Error fetching history');
        console.error(err);
      });
    }

    function closeModal() {
      document.getElementById('historyModal').style.display = 'none';
    }

    window.addEventListener('keydown', function (e) {
      if (e.key === 'Escape') closeModal();
    });
  </script>
</body>
</html>
