<script src="/style/js/roleCheck.js"></script>
<script>
  checkRoleAccess(['gateAdmin', 'superAdmin']); // customize allowed roles per page
</script>

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Gate Management</title>

    <!-- Load jQuery first -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.2/jquery.min.js"></script>

    <!-- Additional Scripts -->
    <script src="../../style/js/plugin.js"></script>
    <script src="../../style/js/bootstrap-datepicker.min.js"></script>
    <script src="../../style/js/clockpicker.js"></script>
    <script src="../../style/js/custom.js"></script>
    <script src="../../style/js/config.js"></script>
    <script src="../../sessionstorage.js"></script>

    <!-- Shared Styles -->
    <link rel="stylesheet" href="../../style/css/styles.css" />
  </head>

  <body>
    <div class="header">
      <div class="container">
        <div class="logout">
          <a href="javascript:void(0);" onclick="history.back()">Back</a>
          &nbsp; | &nbsp;
          <a href="javascript:void(0);" onclick="goToHome()">Home</a>
          &nbsp; | &nbsp;
          <a href="javascript:void(0);" onclick="logout()">Logout</a>
        </div>
      </div>
    </div>

    <div class="middlecontent">
      <div class="container">
        <div class="whitesec">
          <div class="inner-padding">
            <div class="frm-head">
              <h1>Gate Management</h1>
            </div>

            <div class="admin-menu">
              <div class="menu-grid">
                <a class="menu-btn" href="gateIn.html">
                  Gate Checkin
                </a>
                <a class="menu-btn" href="gateOut.html">
                  Gate Checkout
                </a>
                <a class="menu-btn" href="gateInTap.html">
                  Gate Checkin via Tap
                </a>
                <a class="menu-btn" href="gateOutTap.html">
                  Gate Checkout via Tap
                </a>
                <a class="menu-btn" href="gateReport.html">
                    Gate Checkin/out Report
                </a>
                <a class="menu-btn" href="onPremise.html">
                  Current onPremise Report
                </a>
                
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
  </body>
</html>
