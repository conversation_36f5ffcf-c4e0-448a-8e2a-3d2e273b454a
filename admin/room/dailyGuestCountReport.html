<script src="/style/js/roleCheck.js"></script>
<script>
  checkRoleAccess(['officeAdmin', 'roomAdmin', 'superAdmin']); // customize allowed roles per page
</script>

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Daily Guest Count / Availability Report</title>

    <!-- Load jQuery first -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.2/jquery.min.js"></script>

    <!-- Scripts -->
    <script src="https://use.fontawesome.com/373a347df2.js"></script>
    <script src="../../style/js/plugin.js"></script>
    <script src="../../style/js/bootstrap-datepicker.min.js"></script>
    <script src="../../style/js/clockpicker.js"></script>
    <script src="../../style/js/custom.js"></script>
    <script src="../../style/js/config.js"></script>
    <script src="../../style/js/formatDate.js"></script>
    <script src="/sessionstorage.js"></script>
    <script src="dailyGuestCountReport.js"></script>
    <script src="../../style/js/tableSortFilter.js" defer></script>
    <script src="https://cdn.sheetjs.com/xlsx-latest/package/dist/xlsx.full.min.js"></script>
    <script src="../../style/js/exportToExcel.js"></script>


    <!-- Styles -->
    <link rel="stylesheet" href="../../style/css/styles.css" />
  </head>

  <body>
    <div class="header">
      <div class="container">
        <div class="logout">
          <a href="javascript:void(0);" onclick="history.back()">Back</a>
          &nbsp; | &nbsp; <a href="javascript:void(0);" onclick="goToHome()">Home</a>
          &nbsp; | &nbsp; <a href="javascript:void(0);" onclick="logout()">Logout</a>
        </div>
      </div>
    </div>

    <div class="middlecontent">
      <div class="container">
        <div class="whitesec">
          <div class="inner-padding">
            <div class="frm-head">
              <h1>Daily Guest Count / Availability Report</h1>
            </div>

            <div class="form">
              <div id="alert" class="alert" role="alert" style="display: none;"></div>
              <form id="reportForm">
                <div class="form-group">
                  <label for="start_date">Start Date:</label>
                  <input type="date" class="form-control" id="start_date" name="start_date" required />
                </div>

                <div class="form-group">
                  <label for="end_date">End Date:</label>
                  <input type="date" class="form-control" id="end_date" name="end_date" required />
                </div>

                <div class="form-group form-submit">
                  <button type="submit" class="btn btn-primary">Submit</button>
                </div>
              </form>
            </div>

            <div class="frm-head mt-4">
              <h2>Report</h2>
            </div>
<input type="text" id="tableSearch" class="form-control search-box" placeholder="Search...">

            <div class="table-responsive">
              <table id="reportTable" class="table table-striped table-bordered">
                <thead>
                  <tr>
                    <th>#</th>
                    <th>Date</th>
                    <th>Guests in A.C.</th>
                    <th>Guests in Non A.C.</th>
                    <th>Total Guests in RC</th>
                    <th>A.C. Beds Available</th>
                    <th>Non A.C. Beds Available</th>
                    <th>Total Beds Available</th>
                  </tr>
                </thead>
                <tbody id="reportTableBody">
                  <!-- Table rows will be dynamically added here -->
                </tbody>
              </table>
              <div id="downloadBtnContainer"></div>
            </div>

          </div>
        </div>
      </div>
    </div>
  </body>
</html>
