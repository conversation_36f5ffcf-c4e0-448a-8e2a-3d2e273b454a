<script src="/style/js/roleCheck.js"></script>
<script>
  checkRoleAccess(['officeAdmin', 'roomAdmin', 'superAdmin']); // customize allowed roles per page
</script>

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Manage Rooms</title>

    <!-- Load jQuery first -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.2/jquery.min.js"></script>

    <!-- Scripts -->
    <script src="../../style/js/plugin.js"></script>
    <script src="../../style/js/bootstrap-datepicker.min.js"></script>
    <script src="../../style/js/clockpicker.js"></script>
    <script src="../../style/js/custom.js"></script>
    <script src="../../style/js/config.js"></script>
    <script src="/sessionstorage.js"></script>
    <script src="manageRooms.js"></script>
    <script src="../../style/js/tableSortFilter.js" defer></script>

    <!-- Styles -->
    <link rel="stylesheet" href="../../style/css/styles.css" />
  </head>

  <body>
    <div class="header">
      <div class="container">
        <div class="logout">
          <a href="javascript:void(0);" onclick="history.back()">Back</a>
          &nbsp; | &nbsp; <a href="javascript:void(0);" onclick="goToHome()">Home</a>
          &nbsp; | &nbsp; <a href="javascript:void(0);" onclick="logout()">Logout</a>
        </div>
      </div>
    </div>

    <div class="middlecontent">
      <div class="container">
        <div class="whitesec">
          <div class="inner-padding">

            <div class="frm-head">
              <h1>Manage Rooms</h1>
            </div>

            <div class="form">
              <div id="alert" class="alert" role="alert" style="display: none;"></div>
              <input type="text" id="tableSearch" class="form-control search-box" placeholder="Search...">
              <div class="table-responsive">
                <table id="reportTable" class="table table-striped table-bordered">
                  <thead>
                    <tr>
                      <th>#</th>
                      <th>&nbsp;</th>
                      <th>Room No</th>
                      <th>Room Type</th>
                      <th>Gender</th>
                      <th>Status</th>
                      <th>Action</th>
                    </tr>
                  </thead>
                  <tbody id="reportTableBody">
                    <!-- Table rows will be dynamically added here -->
                  </tbody>
                </table>
              </div>

              <!-- <div class="form-group form-submit mt-3">
                <button onclick="window.location.href='addRoom.html'" class="btn btn-secondary">
                  Add New Room
                </button>
              </div> -->

            </div>

          </div>
        </div>
      </div>
    </div>
  </body>
</html>
