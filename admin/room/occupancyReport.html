<script src="/style/js/roleCheck.js"></script>
<script>
  checkRoleAccess(['officeAdmin', 'roomAdmin', 'superAdmin']); // customize allowed roles per page
</script>

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Occupancy Report</title>

    <!-- Load jQuery first -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.2/jquery.min.js"></script>

    <!-- Scripts -->
    <script src="../../style/js/plugin.js"></script>
    <script src="../../style/js/bootstrap-datepicker.min.js"></script>
    <script src="../../style/js/clockpicker.js"></script>
    <script src="../../style/js/custom.js"></script>
    <script src="../../style/js/config.js"></script>
    <script src="../../style/js/formatDate.js"></script>
    <script src="/sessionstorage.js"></script>
    <script src="occupancyReport.js"></script>
    <script src="../../style/js/tableSortFilter.js" defer></script>
    <script src="https://cdn.sheetjs.com/xlsx-latest/package/dist/xlsx.full.min.js"></script>
    <script src="../../style/js/exportToExcel.js"></script>


    <!-- Styles -->
    <link rel="stylesheet" href="../../style/css/styles.css" />
  </head>

  <body>
    <div class="header">
      <div class="container">
        <div class="logout">
          <a href="javascript:void(0);" onclick="history.back()">Back</a>
          &nbsp; | &nbsp; <a href="javascript:void(0);" onclick="goToHome()">Home</a>
          &nbsp; | &nbsp; <a href="javascript:void(0);" onclick="logout()">Logout</a>
        </div>
      </div>
    </div>

    <div class="middlecontent">
      <div class="container">
        <div class="whitesec">
          <div class="inner-padding">

            <div class="frm-head">
              <h1>Occupancy Report</h1>
            </div>
<input type="text" id="tableSearch" class="form-control search-box" placeholder="Search...">

            <div class="table-responsive">
              <table id="occupancyTable" class="table table-striped table-bordered">
                <thead>
                  <tr>
                    <th>#</th>
                    <th>Booking ID</th>
                    <th>Guest Name</th> 
                    <th>Mobile No.</th>
                    <th>Center</th>
                    <th>Room No</th>
                    <th>Room Type</th>
                    <th>Check-in</th>
                    <th>Check-out</th>
                    <th>Nights</th>
                    <th>Booked By</th>
                  </tr>
                </thead>
                <tbody>
                  <!-- Occupancy report data will be dynamically inserted here -->
                </tbody>
              </table>
              <div id="downloadBtnContainer"></div>
            </div>

          </div>
        </div>
      </div>
    </div>
  </body>
</html>
