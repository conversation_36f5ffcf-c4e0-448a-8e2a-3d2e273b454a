<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Room Booking Reports</title>

  <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.2/jquery.min.js"></script>
  <script src="../../style/js/plugin.js"></script>
  <script src="../../style/js/bootstrap-datepicker.min.js"></script>
  <script src="../../style/js/clockpicker.js"></script>
  <script src="../../style/js/custom.js"></script>
  <script src="../../style/js/config.js"></script>
  <script src="../../style/js/formatDate.js"></script>
  <script src="/sessionstorage.js"></script>
  <script src="roomReports.js" defer></script>
  <script src="../../style/js/tableSortFilter.js" defer></script>
  <script src="https://cdn.sheetjs.com/xlsx-latest/package/dist/xlsx.full.min.js"></script>
  <script src="../../style/js/exportToExcel.js"></script>
  <script src="/style/js/roleCheck.js"></script>
  <script>
    checkRoleAccess(['officeAdmin', 'roomAdmin', 'superAdmin']);
  </script>

  <link rel="stylesheet" href="../../style/css/styles.css" />
  <style>
    .modal {
      display: none;
      position: fixed;
      z-index: 9999;
      padding-top: 100px;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      overflow: auto;
      background-color: rgba(0,0,0,0.4);
    }

    .modal-content {
      background-color: #fff;
      margin: auto;
      padding: 20px;
      border: 1px solid #888;
      width: 50%;
      border-radius: 5px;
    }

    .modal-header {
      font-weight: bold;
      margin-bottom: 10px;
    }

    .modal-actions {
      text-align: right;
      margin-top: 15px;
    }

    .modal-actions button {
      margin-left: 10px;
    }
  </style>
</head>
<body>
  <div class="header">
    <div class="container">
      <div class="logout">
        <a href="javascript:void(0);" onclick="history.back()">Back</a> |
        <a href="javascript:void(0);" onclick="goToHome()">Home</a> |
        <a href="javascript:void(0);" onclick="logout()">Logout</a>
      </div>
    </div>
  </div>

  <div class="middlecontent">
    <div class="container">
      <div class="whitesec">
        <div class="inner-padding">
          <div class="frm-head">
            <h1>Room Booking Reports</h1>
          </div>

          <div class="form">
            <div id="alert" class="alert" style="display:none;"></div>
            <form id="reportForm">
              <div class="form-group">
                <label for="report_type">Report Type:</label>
                <select id="report_type" name="report_type" class="form-control" required>
                  <option value="reservation_report" data-type="room">Room Reservation Report</option>
                  <option value="flat_reservation_report" data-type="flat">Flat Reservation Report</option>
                </select>
              </div>

              <div class="form-group">
                <label>Status:</label>
                <div class="checkbox-group" style="display: flex; flex-wrap: wrap; gap: 1rem;">
                  <label><input type="checkbox" name="status" value="checkedin" checked /> Checked-in</label>
                  <label><input type="checkbox" name="status" value="checkedout" /> Checked-out</label>
                  <label><input type="checkbox" name="status" value="pending checkin" /> Pending Check-in</label>
                  <label><input type="checkbox" name="status" value="waiting" /> Waiting</label>
                  <label><input type="checkbox" name="status" value="pending" /> Pending</label>
                  <label><input type="checkbox" name="status" value="cancelled" /> Cancelled By User</label>
                  <label><input type="checkbox" name="status" value="admin cancelled" /> Cancelled By Admin</label>
                </div>
              </div>

              <div class="form-group">
                <label for="start_date">Start Date:</label>
                <input type="date" id="start_date" name="start_date" class="form-control" required />
              </div>

              <div class="form-group">
                <label for="end_date">End Date:</label>
                <input type="date" id="end_date" name="end_date" class="form-control" required />
              </div>

              <div class="form-group form-submit">
                <button type="submit" class="btn btn-primary">Submit</button>
                <div id="downloadBtnContainer"></div>
              </div>
            </form>
          </div>

          <div class="table-responsive">
            <input type="text" id="tableSearch" class="form-control search-box" placeholder="Search...">
            <table id="reportTable" class="table table-striped table-bordered">
              <thead>
                <tr>
                  <th></th>
                  <th>Guest Name</th>
                  <th>Mobile No.</th>
                  <th>Center</th>
                  <th>Room No</th>
                  <th>Room Type</th>
                  <th>Check-in</th>
                  <th>Check-out</th>
                  <th>Nights</th>
                  <th>Status</th>
                  <th>Booked By</th>
                  <th>Action</th>
                  <th>Cancel</th>
                </tr>
              </thead>
              <tbody id="reportTableBody">
                <!-- JS will populate -->
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal for "waiting" status update -->
<!-- Modal for "waiting" status update -->
<div id="roomUpdateModal" class="modal">
  <div class="modal-content">
    <div class="modal-header">Update Booking Status</div>
    <form id="roomStatusForm">
      <div class="form-group">
        <label for="modal_bookingid_display">Booking ID:</label>
        <input type="text" id="modal_bookingid_display" class="form-control" readonly disabled />
        <input type="hidden" id="modal_bookingid" />
      </div>

      <div class="form-group">
        <label for="modal_credits">Room Credits Available:</label>
        <input type="text" id="modal_credits" class="form-control" readonly disabled />
      </div>

      <div class="form-group">
        <label for="modal_base_amount">Base Amount (₹):</label>
        <input type="text" id="modal_base_amount" class="form-control" readonly disabled />
      </div>

      <div class="form-group">
        <label for="modal_credits_used">Credits Used (₹):</label>
        <input type="text" id="modal_credits_used" class="form-control" readonly disabled />
      </div>

      <div class="form-group">
        <label for="modal_discounted_amount">Amount After Credits (₹):</label>
        <input type="text" id="modal_discounted_amount" class="form-control" readonly disabled />
      </div>

      <div class="form-group">
        <label for="modal_status">New Status:</label>
        <select id="modal_status" required class="form-control">
          <option value="">-- Select --</option>
        </select>
      </div>

      <div class="form-group">
        <label for="modal_description">Remarks (optional):</label>
        <textarea id="modal_description" class="form-control" placeholder="Any note or reason..."></textarea>
      </div>

      <!-- ✅ New Room No Field (conditionally visible) -->
      <div class="form-group" id="modal_roomno_group" style="display: none;">
        <label for="modal_roomno">Room No:</label>
        <input type="text" id="modal_roomno" class="form-control" />
      </div>

      <div class="modal-actions">
        <button type="button" id="closeRoomModal" class="btn btn-secondary">Close</button>
        <button type="submit" class="btn btn-primary">Submit</button>
      </div>
    </form>
  </div>
</div>

  <!-- Init features -->
  <script type="module">
    document.addEventListener('DOMContentLoaded', () => {
      enhanceTable('reportTable', 'tableSearch');
    });
  </script>
</body>
</html>
