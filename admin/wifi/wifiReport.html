<script src="/style/js/roleCheck.js"></script>
<script>
  checkRoleAccess(['wifiAdmin', 'superAdmin']); // customize allowed roles per page
</script>

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>WiFi Codes Usage Report</title>

    <!-- jQuery -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.2/jquery.min.js"></script>

    <!-- Additional Scripts -->
    <script src="../../style/js/plugin.js"></script>
    <script src="../../style/js/bootstrap-datepicker.min.js"></script>
    <script src="../../style/js/clockpicker.js"></script>
    <script src="../../style/js/custom.js"></script>
    <script src="../../style/js/config.js"></script>
    <script src="../../sessionstorage.js"></script>
    <script src="../../style/js/tableSortFilter.js" defer></script>
    <script src="wifiReport.js" defer></script>
    <script src="/style/js/roleCheck.js"></script>
    <script src="https://cdn.sheetjs.com/xlsx-latest/package/dist/xlsx.full.min.js"></script>
    <script src="../../style/js/exportToExcel.js"></script>


    <!-- Styles -->
    <link rel="stylesheet" href="../../style/css/styles.css" />

    <style>
      .table-responsive {
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
      }

      .table {
        min-width: 1200px;
        width: 100%;
        table-layout: auto;
      }

      .filter-bar {
        margin: 10px 0;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 10px;
      }

      .code-count-line {
        margin: 5px 0 15px;
        font-weight: bold;
      }
    </style>

    <script>
      checkRoleAccess(['wifiAdmin', 'superAdmin']);
    </script>
  </head>

  <body>
    <div class="header">
      <div class="container">
        <div class="logout">
          <a href="javascript:void(0);" onclick="history.back()">Back</a>
          &nbsp; | &nbsp;
          <a href="javascript:void(0);" onclick="goToHome()">Home</a>
          &nbsp; | &nbsp;
          <a href="javascript:void(0);" onclick="logout()">Logout</a>
        </div>
      </div>
    </div>

    <div class="middlecontent">
      <div class="container">
        <div class="whitesec">
          <div class="inner-padding">
            <div class="frm-head">
              <h1>WiFi Codes Usage Report</h1>
            </div>

            <div class="form">
              <!-- Search -->
              <input
                type="text"
                id="tableSearch"
                class="form-control search-box"
                placeholder="Search..."
              />
              <br />

              <!-- Filter line -->
              
              <div class="filter-bar">
  <label for="statusFilter"><strong>Filter by status:</strong></label>
  <select id="statusFilter">
    <option value="inactive" selected>Inactive</option>
    <option value="active">Active</option>
    <option value="all">All</option>
  </select>

  <label><strong>Booking Type:</strong></label>
  <select id="bookingTypeFilter">
    <option value="all" selected>All</option>
    <option value="room">Room</option>
    <option value="flat">Flat</option>
  </select>

  <label><strong>Date Range:</strong></label>
  <input
    type="text"
    id="startDate"
    class="form-control datepicker"
    placeholder="From"
    style="width: 130px"
  />
  <span>to</span>
  <input
    type="text"
    id="endDate"
    class="form-control datepicker"
    placeholder="To"
    style="width: 130px"
  />

  <button id="applyDateFilter" class="btn btn-primary btn-sm">Filter</button>

  <div id="downloadBtnContainer" style="margin-top: 10px;"></div>
</div>

              <!-- Count line -->
              <div class="code-count-line" id="activeCount">Active Codes: 0 | Inactive Codes: 0</div>
            </div>

            <!-- Table -->
            <div class="table-responsive">
              <table
                id="gateRecordTable"
                class="table table-striped table-bordered"
              >
                <thead>
                  <tr>
                    <th>#</th>
                    <th>Password</th>
                    <th>Status</th>
                    <th>Code Issued At</th>
                    <th>Name</th>
                    <th>Mobile Number</th>
                    <th>Checkin Date</th>
                    <th>Checkout Date</th>
                  </tr>
                </thead>
                <tbody id="gateRecords"></tbody>
              </table>
            </div>

          </div>
        </div>
      </div>
    </div>
  </body>
</html>
