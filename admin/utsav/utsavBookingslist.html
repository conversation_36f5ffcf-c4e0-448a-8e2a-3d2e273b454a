<!-- Keep all your original includes intact -->
<script src="/style/js/roleCheck.js"></script>
<script>
  checkRoleAccess(['utsavAdmin', 'superAdmin']);
</script>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Utsav Bookings</title>

  <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.2/jquery.min.js"></script>
  <link rel="stylesheet" href="../../style/css/styles.css" />

  <script src="../../style/js/plugin.js"></script>
  <script src="../../style/js/bootstrap-datepicker.min.js"></script>
  <script src="../../style/js/clockpicker.js"></script>
  <script src="../../style/js/custom.js"></script>
  <script src="../../style/js/config.js"></script>
  <script src="/sessionstorage.js"></script>
  <script src="utsavBookingslist.js"></script>
  <script src="../../style/js/tableSortFilter.js"></script>
  <script src="https://cdn.sheetjs.com/xlsx-latest/package/dist/xlsx.full.min.js"></script>
  <script src="../../style/js/exportToExcel.js"></script>
</head>

<body>
  <div class="header">
    <div class="container">
      <div class="logout">
        <a href="javascript:void(0);" onclick="history.back()">Back</a> &nbsp; | &nbsp;
        <a href="javascript:void(0);" onclick="goToHome()">Home</a> &nbsp; | &nbsp;
        <a href="javascript:void(0);" onclick="logout()">Logout</a>
      </div>
    </div>
  </div>

  <div class="middlecontent">
    <div class="container">
      <div class="whitesec">
        <div class="inner-padding">
          <div class="frm-head">
            <h1>Utsav Bookings <span id="utsavName"></span></h1>
          </div>

          <div class="form">
  <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px; flex-wrap: wrap;">
  <label for="packageFilter"><b>Filter by Package:</b></label>
  <select id="packageFilter"></select>
  <button id="downloadAll" class="btn btn-primary">Download All Registrations</button>
  <button id="downloadPackage" class="btn btn-primary">Download This Package Registrations</button>
  <button id="centerSummaryBtn" class="btn btn-secondary">Center-wise Summary</button>
</div>


<input type="text" id="tableSearch" class="form-control search-box" placeholder="Search...">

<div class="table-responsive" id="tableContainer">
              <!-- Tables inserted here dynamically -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Center-wise Summary Modal -->
<div id="centerSummaryModal" class="modal" style="display:none;">
  <div class="modal-content">
    <span onclick="document.getElementById('centerSummaryModal').style.display='none'">&times;</span>
    <h3>Center-wise Summary</h3>
    <div id="centerSummaryTableContainer"></div>
  </div>
</div>

<style>
  .modal {
    position: fixed;
    z-index: 999;
    left: 0; top: 0;
    width: 100%; height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.4);
  }
  .modal-content {
    max-width: 600px;
    margin: 5% auto;
    padding: 20px;
    background: white;
    border-radius: 10px;
    position: relative;
  }
  .modal-content span {
    position: absolute;
    top: 10px;
    right: 20px;
    font-size: 24px;
    cursor: pointer;
  }
  #centerSummaryTableContainer table {
    width: 100%;
    border-collapse: collapse;
  }
  #centerSummaryTableContainer th, #centerSummaryTableContainer td {
    border: 1px solid #ccc;
    padding: 8px;
    text-align: center;
  }
  #centerSummaryTableContainer th {
    background-color: #f2f2f2;
  }
</style>

</body>
</html>
