<script src="/style/js/roleCheck.js"></script>
<script>
  checkRoleAccess(['maintenanceAdmin','electricalAdmin','housekeepingAdmin', 'superAdmin']); // customize allowed roles per page
</script>

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Maintenance Requests</title>

    <!-- jQuery -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.2/jquery.min.js"></script>

    <!-- Additional Scripts -->
    <script src="../../style/js/plugin.js"></script>
    <script src="../../style/js/bootstrap-datepicker.min.js"></script>
    <script src="../../style/js/clockpicker.js"></script>
    <script src="../../style/js/custom.js"></script>
    <script src="../../sessionstorage.js"></script>
    <script src="../../style/js/config.js"></script>
    <script src="maintenance.js" defer></script>
    <script src="../../style/js/tableSortFilter.js" defer></script>


    <!-- Shared Styles -->
    <link rel="stylesheet" href="../../style/css/styles.css" />
    <style>
      .table-responsive {
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
      }

      .table {
        min-width: 1000px; /* Or adjust based on expected column count */
        width: 100%;
      }
    </style>
  </head>

  <body>
    <div class="header">
      <div class="container">
        <div class="logout">
          <a href="javascript:void(0);" onclick="history.back()">Back</a>
          &nbsp; | &nbsp;
          <a href="javascript:void(0);" onclick="goToHome()">Home</a>
          &nbsp; | &nbsp;
          <a href="javascript:void(0);" onclick="logout()">Logout</a>
        </div>
      </div>
    </div>

    <div class="middlecontent">
      <div class="container">
        <div class="whitesec">
          <div class="inner-padding">
            <div class="frm-head">
              <h1>All Requests</h1>
            </div>
<input type="text" id="tableSearch" class="form-control search-box" placeholder="Search...">
  
            <div class="form">
  <div class="table-responsive">
    <table id="maintenanceTable" class="table table-striped table-bordered">
      <thead>
                    <tr>
                      <th>Sr No</th>
                      <th>Requested By</th>
                      <th>Mobile No.</th>
                      <th>Created At</th>
                      <th>Department</th>
                      <th>Area of Work</th>
                      <th>Work Detail</th>
                      <th>Comments</th>
                      <th>Closed at</th>
                      <th>Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td colspan="10">Loading...</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

  </body>
</html>
