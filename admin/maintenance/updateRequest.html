<script src="/style/js/roleCheck.js"></script>
<script>
  checkRoleAccess(['maintenanceAdmin', 'electricalAdmin' , 'housekeepingAdmin' , 'superAdmin']); // customize allowed roles per page
</script>

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Update Maintenance Request</title>

    <!-- jQuery -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.2/jquery.min.js"></script>

    <!-- Additional Scripts -->
    <script src="../../style/js/plugin.js"></script>
    <script src="../../style/js/bootstrap-datepicker.min.js"></script>
    <script src="../../style/js/clockpicker.js"></script>
    <script src="../../style/js/custom.js"></script>
    <script src="../../sessionstorage.js"></script>
    <script src="../../style/js/config.js"></script>
    <script src="updateRequest.js" defer></script>

    <!-- Shared Styles -->
    <link rel="stylesheet" href="../../style/css/styles.css" />
  </head>

  <body>
    <!-- Header -->
    <div class="header">
      <div class="container">
        <div class="logout">
          <a href="javascript:void(0);" onclick="history.back()">Back</a>
          &nbsp; | &nbsp;
          <a href="/admin/">Home</a>
          &nbsp; | &nbsp;
          <a id="homelink" href="/admin/">Logout</a>
        </div>
      </div>
    </div>

    <!-- Content -->
    <div class="middlecontent">
      <div class="container">
        <div class="whitesec">
          <div class="inner-padding">

            <div class="frm-head">
              <h1>Update Maintenance Request</h1>
            </div>

            <div class="form">
              <div id="alert" class="alert" role="alert"></div>

              <form id="updateForm">
                <div class="form-group">
                  <label for="issuedto">Issued To</label>
                  <input type="text" class="form-control" id="issuedto" name="issuedto" readonly />
                </div>

                <div class="form-group">
                  <label for="department">Department</label>
                  <input type="text" class="form-control" id="department" name="department" readonly />
                </div>

                <div class="form-group">
                  <label for="comments">Comments</label>
                  <textarea class="form-control" id="comments" name="comments" rows="4" required></textarea>
                </div>

                <div class="form-group">
                  <label for="status">Status</label>
                  <select class="form-control" id="status" name="status" required>
                    <option value="open">Open</option>
                    <option value="closed">Closed</option>
                  </select>
                </div>

                <div class="form-group form-submit">
                  <button type="submit" class="btn btn-primary">Submit</button>
                </div>
              </form>

            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
