<script src="/style/js/roleCheck.js"></script>
<script>
  checkRoleAccess(['avtAdmin', 'superAdmin']); // customize allowed roles per page
</script>

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AVT Management</title>

    <!-- Load jQuery first -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.2/jquery.min.js"></script>

    <!-- Additional Scripts -->
    <script src="../../style/js/plugin.js"></script>
    <script src="../../style/js/bootstrap-datepicker.min.js"></script>
    <script src="../../style/js/clockpicker.js"></script>
    <script src="../../style/js/custom.js"></script>
    <script src="../../style/js/config.js"></script>
    <script src="../../sessionstorage.js"></script>
    <script src="index.js"></script>

    <!-- Shared Styles -->
    <link rel="stylesheet" href="../../style/css/styles.css" />
  </head>
  <body>
    <div class="header">
      <div class="container">
        <div class="logout">
          <a href="javascript:void(0);" onclick="history.back()">Back</a>
          &nbsp; | &nbsp; <a href="javascript:void(0);" onclick="goToHome()">Home</a> &nbsp; | &nbsp;
          <a href="javascript:void(0);" onclick="logout()">Logout</a>
        </div>
      </div>
    </div>

    <div class="middlecontent">
      <div class="container">
        <div class="whitesec">
          <div class="inner-padding">
            <div class="frm-head">
              <h1>AVT Management</h1>
            </div>
            <div class="form">
              <div class="form-group">
                <label for="search">Search by name:</label>
                <input
                  type="text"
                  id="search"
                  class="form-control"
                  placeholder="Enter name to search"
                />
              </div>

              <div class="table-responsive">
                <table
                  id="data-list"
                  class="table table-striped table-bordered"
                >
                  <thead>
                    <tr>
                      <th>Name</th>
                      <th>Card Number</th>
                      <th>Mobile Number</th>
                      <th>Email Id</th>
                      <th>Photo Link</th>
                    </tr>
                  </thead>
                  <tbody>
                    <!-- Rows populated by JavaScript -->
                  </tbody>
                </table>
              </div>

                
              </div>
            </div>
          </body>