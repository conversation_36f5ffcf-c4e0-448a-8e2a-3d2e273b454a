<script src="/style/js/roleCheck.js"></script>
<script>
  checkRoleAccess(['officeAdmin', 'adhyayanAdmin', 'superAdmin']); // customize allowed roles per page
</script>

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Upcoming Adhyayans</title>

    <!-- Load jQuery first -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.2/jquery.min.js"></script>

    <!-- Scripts -->
    <script src="../../style/js/plugin.js"></script>
    <script src="../../style/js/bootstrap-datepicker.min.js"></script>
    <script src="../../style/js/clockpicker.js"></script>
    <script src="../../style/js/custom.js"></script>
    <script src="../../style/js/config.js"></script>
    <script src="../../style/js/formatDate.js"></script>
    <script src="/sessionstorage.js"></script>
    <script src="fetchAllAdhyayan.js"></script>

    <!-- Styles -->
    <link rel="stylesheet" href="../../style/css/styles.css" />
  </head>

  <body>
    <div class="header">
      <div class="container">
        <div class="logout">
          <a href="javascript:void(0);" onclick="history.back()">Back</a>
          &nbsp; | &nbsp; <a href="javascript:void(0);" onclick="goToHome()">Home</a>
          &nbsp; | &nbsp; <a href="javascript:void(0);" onclick="logout()">Logout</a>
        </div>
      </div>
    </div>

    <div class="middlecontent">
      <div class="container">
        <div class="whitesec">
          <div class="inner-padding">
            <div class="frm-head">
              <h1>Upcoming Adhyayans at Research Center</h1>
            </div>

            <div class="form">
              <div class="table-responsive">
                <table id="adhyayanList" class="table table-striped table-bordered">
                  <thead>
                    <tr>
                      <th><center>Serial No</center></th>
                      <th><center>Adhyayan Name</center></th>
                      <th><center>Location</center></th>
                      <th><center>Swadhyay Karta</center></th>
                      <th><center>Start Date</center></th>
                      <th><center>End Date</center></th>
                      <th><center>Max Count</center></th>
                      <th><center>Status</center></th>
                      <th><center>Action</center></th>
                    </tr>
                  </thead>
                  <tbody>
                    <!-- Rows will be populated dynamically using JavaScript -->
                  </tbody>
                </table>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
  </body>
</html>
