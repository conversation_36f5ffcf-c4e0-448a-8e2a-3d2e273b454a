<script src="/style/js/roleCheck.js"></script>
<script>
  checkRoleAccess(['officeAdmin', 'adhyayanAdmin', 'superAdmin', 'adhyayanAd<PERSON><PERSON><PERSON>', 'adhyayanAdmin<PERSON>aj', 'adhyayanAdmin<PERSON>hu']); // customize allowed roles per page
</script>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Adhyayan Bookings</title>

  <!-- Load jQuery first -->
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.2/jquery.min.js"></script>

  
  <!-- Styles -->
  <link rel="stylesheet" href="../../style/css/styles.css" />

  <!-- Additional Scripts -->
  <script src="../../style/js/plugin.js"></script>
  <script src="../../style/js/bootstrap-datepicker.min.js"></script>
  <script src="../../style/js/clockpicker.js"></script>
  <script src="../../style/js/custom.js"></script>
  <script src="../../style/js/config.js"></script>
  <script src="/sessionstorage.js"></script>
  <script src="adhyayanBookingslist.js"></script>
  <script src="../../style/js/tableSortFilter.js" defer></script>
  <script src="https://cdn.sheetjs.com/xlsx-latest/package/dist/xlsx.full.min.js"></script>
  <script src="../../style/js/exportToExcel.js"></script>

</head>
<body>
  <div class="header">
    <div class="container">
      <div class="logout">
        <a href="javascript:void(0);" onclick="history.back()">Back</a>
        &nbsp; | &nbsp; <a href="javascript:void(0);" onclick="goToHome()">Home</a> &nbsp; | &nbsp;
        <a href="javascript:void(0);" onclick="logout()">Logout</a>
      </div>
    </div>
  </div>

  <div class="middlecontent">
    <div class="container">
      <div class="whitesec">
        <div class="inner-padding">
          <div class="frm-head">
            <h1>Adhyayan Bookings <span id="shibirName"></span></h1>
          </div>

          <div class="form">
            <p id="genderCount">Males: 0 | Females: 0</p>


            <input type="text" id="tableSearch" class="form-control search-box" placeholder="Search...">
            <div class="table-responsive">
              <table id="waitlistTable" class="table table-striped table-bordered">
  <thead>
    <tr>
      <th>Sr</th>
      <th>Booking ID</th>
      <th>Name</th>
      <th>Mobile</th>
      <th>Gender</th>
      <th>Center</th>
      <th>Res Status</th>
      <th>Status</th>
      <th>Booked By</th>
      <th>Action</th>
    </tr>
  </thead>
  <tbody></tbody>
</table>

<div id="genderCount" style="margin-top: 10px;"></div>
<div id="downloadBtnContainer" style="margin-top: 10px;"></div>
            </div>
          </div>

        </div>
      </div>
    </div>
  </div>
</body>
</html>
