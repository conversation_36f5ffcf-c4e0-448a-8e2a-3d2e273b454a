<script src="/style/js/roleCheck.js"></script>
<script>
  checkRoleAccess(['officeAdmin', 'adhyayanAdmin', 'superAdmin', 'adhyayanAd<PERSON><PERSON>ol', 'adhyayanAdminRaj', 'adhyayanAdminDhu']); // customize allowed roles per page
</script>

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Adhyayan Status Update</title>

    <!-- Load jQuery first -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.2/jquery.min.js"></script>

    <!-- Scripts -->
    <script src="../../style/js/plugin.js"></script>
    <script src="../../style/js/bootstrap-datepicker.min.js"></script>
    <script src="../../style/js/clockpicker.js"></script>
    <script src="../../style/js/custom.js"></script>
    <script src="../../style/js/config.js"></script>
    <script src="/sessionstorage.js"></script>
    <script src="adhyayanStatusUpdate.js"></script>

    <!-- Styles -->
    <link rel="stylesheet" href="../../style/css/styles.css" />
  </head>

  <body>
    <div class="header">
      <div class="container">
        <div class="logout">
          <a href="javascript:void(0);" onclick="history.back()">Back</a>
          &nbsp; | &nbsp; <a href="javascript:void(0);" onclick="goToHome()">Home</a>
          &nbsp; | &nbsp; <a href="javascript:void(0);" onclick="logout()">Logout</a>
        </div>
      </div>
    </div>

    <div class="middlecontent">
      <div class="container">
        <div class="whitesec">
          <div class="inner-padding">

            <div class="frm-head">
              <h1>Adhyayan Status Update</h1>
            </div>

            <div class="form">
              <form id="statusForm">

                <div class="form-group">
                  <label for="shibir_id">Shibir ID:</label>
                  <input type="text" id="shibir_id" name="shibir_id" class="form-control" required />
                </div>

                <div class="form-group">
                  <label for="bookingid">Booking ID:</label>
                  <input type="text" id="bookingid" name="bookingid" class="form-control" required />
                </div>

                <div class="form-group">
                  <label for="status">Status:</label>
                  <select id="status" name="status" class="form-control" required>
                    <option value="confirmed">Confirmed</option>
                    <option value="admin cancelled">Admin Cancelled</option>
                    <option value="pending">Payment Pending</option>
                    <option value="waiting">Waiting</option>
                  </select>
                </div>

                
                <div class="form-group">
                  <label for="description">Description:</label>
                  <textarea id="description" name="description" rows="4" class="form-control"></textarea>
                </div>

                <div class="form-group form-submit">
                  <button type="submit" id="saveButton" name="saveButton" class="btn btn-primary">
                    Update Status
                  </button>
                </div>
              </form>

              <p id="statusMessage"></p>
            </div>

          </div>
        </div>
      </div>
    </div>
  </body>
</html>
