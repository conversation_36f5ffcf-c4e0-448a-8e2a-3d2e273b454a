<script src="/style/js/roleCheck.js"></script>
<script>
  checkRoleAccess(['officeAdmin', 'adhyayanAdmin', 'superAdmin']); // customize allowed roles per page
</script>

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Adhyayan Management</title>

    <!-- Load jQuery first -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.2/jquery.min.js"></script>

    <!-- Additional Scripts -->
    <script src="../../style/js/plugin.js"></script>
    <script src="../../style/js/bootstrap-datepicker.min.js"></script>
    <script src="../../style/js/clockpicker.js"></script>
    <script src="../../style/js/custom.js"></script>
    <script src="../../style/js/config.js"></script>
    <script src="../../sessionstorage.js"></script>

    <!-- Shared Styles -->
    <link rel="stylesheet" href="../../style/css/styles.css" />
  </head>

  <body>
    <div class="header">
      <div class="container">
        <div class="logout">
          <a href="javascript:void(0);" onclick="history.back()">Back</a>
          &nbsp; | &nbsp;
          <a href="javascript:void(0);" onclick="goToHome()">Home</a>
          &nbsp; | &nbsp;
          <a href="javascript:void(0);" onclick="logout()">Logout</a>
        </div>
      </div>
    </div>

    <div class="middlecontent">
      <div class="container">
        <div class="whitesec">
          <div class="inner-padding">
            <div class="frm-head">
              <h1>Adhyayan Management</h1>
            </div>

            <div class="admin-menu">
              <div class="menu-grid">
                <a class="menu-btn" href="createAdhyayan.html">Add New Adhyayan</a>
                <a class="menu-btn" href="fetchAllAdhyayan.html">Edit Existing Adhyayan</a>
                <a class="menu-btn" href="adhyayanReport.html">RC Adhyayan Summary Report</a>
                <a class="menu-btn" href="outstationadhyayanReport.html">Outstation Adhyayan Summary Report</a>
                <a class="menu-btn" href="adhyayanStatusUpdate.html">Adhyayan Status Update</a>
              </div>
            </div>
            
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
