<script src="/style/js/roleCheck.js"></script>
<script>
  checkRoleAccess(['officeAdmin', 'adhyayanAdmin', 'superAdmin']); // customize allowed roles per page
</script>

<!DOCTYPE html>
<html lang="en">
  <head>
    <title>Open/Close Adhyayan</title>

    <!-- Load jQuery first -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.2/jquery.min.js"></script>

    
    <!-- Additional Styles and Scripts -->

    <script src="../../style/js/plugin.js"></script>
    <script src="../../style/js/bootstrap-datepicker.min.js"></script>
    <script src="../../style/js/clockpicker.js"></script>
    <script src="../../style/js/custom.js"></script>
    <script src="../../style/js/config.js"></script>
    <script src="/sessionstorage.js"></script>
    <script src="openCloseAdhyayan.js"></script>
  </head>

  <body>
    <div class="header">
      <div class="container">
        <div class="logout">
          <a href="javascript:void(0);" onclick="history.back()">Back</a>
          &nbsp; | &nbsp; <a href="javascript:void(0);" onclick="goToHome()">Home</a> &nbsp; | &nbsp;
          <a href="javascript:void(0);" onclick="logout()">Logout</a>
        </div>
      </div>
    </div>

    <div class="middlecontent">
      <div class="container">
        <div class="whitesec">
          <div class="inner-padding">
            <div class="frm-head">
              <h4>
                <b><u>Open or Close Adhyayan</u></b>
              </h4>
            </div>

            <div class="form">
              <div class="row">
                <div class="col-md-12">
                  <form id="openCloseForm">
                    <label for="shibir_id">Shibir ID:</label>
                    <input
                      type="text"
                      id="shibir_id"
                      name="shibir_id"
                      required
                    />

                    <label for="status">Status:</label>
                    <select id="status" name="status" required>
                      <option value="open">Open</option>
                      <option value="close">Closed</option>
                    </select>

                    <!-- <button type="submit">Update Status</button> -->
                    <button type="submit" class="btn btn-primary">
                      Update Status
                    </button>
                  </form>

                  <p id="responseMessage"></p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
