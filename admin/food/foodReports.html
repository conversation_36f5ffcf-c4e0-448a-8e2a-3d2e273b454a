<script src="/style/js/roleCheck.js"></script>
<script>
  checkRoleAccess(['foodAdmin', 'superAdmin']); // customize allowed roles per page
</script>

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Food Reports</title>

    <!-- Load jQuery first -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.2/jquery.min.js"></script>

    <!-- Scripts -->
    <script src="../../style/js/plugin.js"></script>
    <script src="../../style/js/bootstrap-datepicker.min.js"></script>
    <script src="../../style/js/clockpicker.js"></script>
    <script src="../../style/js/custom.js"></script>
    <script src="../../style/js/config.js"></script>
    <script src="/sessionstorage.js"></script>
    <script src="foodReports.js"></script>

    <!-- API Config -->
    <script>
      const REPORT_URL = `${CONFIG.basePath}//stay/reservation_report`;
    </script>

    <!-- Styles -->
    <link rel="stylesheet" href="../../style/css/styles.css" />
  </head>

  <body>
    <div class="header">
      <div class="container">
        <div class="logout">
          <a href="javascript:void(0);" onclick="history.back()">Back</a>
          &nbsp; | &nbsp; <a href="javascript:void(0);" onclick="goToHome()">Home</a> &nbsp; | &nbsp;
          <a href="javascript:void(0);" onclick="logout()">Logout</a>
        </div>
      </div>
    </div>

    <div class="middlecontent">
      <div class="container">
        <div class="whitesec">
          <div class="inner-padding">
            <div class="frm-head">
              <h1>Food Reports</h1>
            </div>

            <div class="form">
              <div id="alert" class="alert" role="alert" style="display: none;"></div>
              <form id="foodReportsForm">
                <div class="row">
                  <div class="form-group">
                    <label for="report_type">Report Type:</label>
                    <select id="report_type" name="report_type" class="form-control" required>
                      <option value="foodReport">Food Report</option>
                    </select>
                  </div>

                  <div class="form-group">
                    <label for="start_date">Start Date:</label>
                    <input type="date" id="start_date" name="start_date" class="form-control" required />
                  </div>

                  <div class="form-group">
                    <label for="end_date">End Date:</label>
                    <input type="date" id="end_date" name="end_date" class="form-control" required />
                  </div>

                  <div class="form-group form-submit">
                    <button type="submit" class="btn btn-primary">Submit</button>
                  </div>
                </div>
              </form>
            </div>

            <div class="table-responsive" id="reportResult" style="display: none;">
              <!-- Optional table for results to be rendered dynamically -->
              <table id="reportTable" class="table table-striped table-bordered">
                <thead>
                  <tr>
                    <th>Date</th>
                    <th>Type</th>
                    <th>Count</th>
                    <!-- Add other columns as needed -->
                  </tr>
                </thead>
                <tbody>
                  <!-- Rows will be inserted by JavaScript -->
                </tbody>
              </table>
            </div>

          </div>
        </div>
      </div>
    </div>
  </body>
</html>
