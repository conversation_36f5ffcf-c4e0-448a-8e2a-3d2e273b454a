<script src="/style/js/roleCheck.js"></script>
<script>
  checkRoleAccess(['foodAdmin', 'superAdmin']); // customize allowed roles per page
</script>

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Issued Food Plate Report</title>

    <!-- Load jQuery first -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.2/jquery.min.js"></script>

    <!-- Scripts -->
    <script src="../../style/js/plugin.js"></script>
    <script src="../../style/js/bootstrap-datepicker.min.js"></script>
    <script src="../../style/js/clockpicker.js"></script>
    <script src="../../style/js/custom.js"></script>
    <script src="../../style/js/config.js"></script>
    <script src="../../style/js/formatDate.js"></script>
    <script src="/sessionstorage.js"></script>
    <script src="issuedPlateReport.js"></script>
    <script src="../../style/js/tableSortFilter.js" defer></script>
    
    
    <!-- Styles -->
    <link rel="stylesheet" href="../../style/css/styles.css" />
  </head>

  <body>
    <div class="header">
      <div class="container">
        <div class="logout">
          <a href="javascript:void(0);" onclick="history.back()">Back</a>
          &nbsp; | &nbsp; <a href="javascript:void(0);" onclick="goToHome()">Home</a> &nbsp; | &nbsp;
          <a href="javascript:void(0);" onclick="logout()">Logout</a>
        </div>
      </div>
    </div>

    <div class="middlecontent">
      <div class="container">
        <div class="whitesec">
          <div class="inner-padding">

            <div class="frm-head">
              <h1 id="reportTitle">Issued Food Plate Report</h1>
            </div>

            <div id="alert" class="alert" role="alert" style="display: none;"></div>
<div class="frm-head">
  <h1 id="reportTitle">Issued Food Plate Report</h1>
  <div id="alertBox" style="display: none; padding: 10px; margin-top: 10px; border-radius: 5px; font-weight: bold;"></div>
</div>
<input type="text" id="tableSearch" class="form-control search-box" placeholder="Search...">

            <div class="table-responsive">
              <table id="bookingsTable" class="table table-striped table-bordered">
                <thead>
                  <tr>
                    <th>#</th>
                    <th>Date</th>
                    <th>Name</th>
                    <th>Mobile No</th>
                  </tr>
                </thead>
                <tbody id="reportTableBody">
                  <!-- Table rows will be dynamically populated -->
                </tbody>
              </table>
            </div>

          </div>
        </div>
      </div>
    </div>
  </body>
</html>
