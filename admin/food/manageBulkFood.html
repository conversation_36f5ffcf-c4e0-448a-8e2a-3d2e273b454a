<script src="/style/js/roleCheck.js"></script>
<script>
  checkRoleAccess(['foodAdmin', 'superAdmin']); // customize allowed roles per page
</script>

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Guest Food Booking (Bulk)</title>

    <!-- Load jQuery first -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.2/jquery.min.js"></script>
    

    <!-- Scripts -->
    <script src="../../style/js/plugin.js"></script>
    <script src="../../style/js/bootstrap-datepicker.min.js"></script>
    <script src="../../style/js/clockpicker.js"></script>
    <script src="../../style/js/custom.js"></script>
    <script src="../../style/js/config.js"></script>
    <script src="../../style/js/formatDate.js"></script>
    <script src="/sessionstorage.js"></script>
    <script src="manageBulkFood.js"></script>
    <script src="../../style/js/tableSortFilter.js" defer></script>
    

    <!-- Styles -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" />
    <link rel="stylesheet" href="../../style/css/styles.css" />
  </head>

  <body>
    <div class="header">
      <div class="container">
        <div class="logout">
          <a href="javascript:void(0);" onclick="history.back()">Back</a>
          &nbsp; | &nbsp; <a href="javascript:void(0);" onclick="goToHome()">Home</a>
          &nbsp; | &nbsp; <a href="javascript:void(0);" onclick="logout()">Logout</a>
        </div>
      </div>
    </div>

    <div class="middlecontent">
      <div class="container">
        <div class="whitesec">
          <div class="inner-padding">

            <div class="frm-head">
              <h1>Guest Food Booking (Bulk)</h1>
            </div>

            <div class="form">
              <div id="alert" class="alert" role="alert" style="display:none;"></div>
              <form id="bulkFoodBookingForm">
                <div class="form-group">
                  <label for="cardno">Card No:</label>
                  <input type="tel" class="form-control" id="cardno" name="cardno" required />
                </div>

                <div class="form-group">
                  <label for="date">Date:</label>
                  <input type="date" class="form-control" id="date" name="date" required />
                </div>

                <div class="form-group">
                  <label for="guestCount">Guest Count:</label>
                  <input type="text" class="form-control" id="guestCount" name="guestCount" required />
                </div>

                <div class="form-group">
                  <label for="department">Department:</label>
                  <select id="department" name="department" class="form-control" required>
                    <option value='RC' selected>Research Center Guest</option>
                    <option value='SREC'>School Guest</option>
                    <option value='SRMC'>Medical Guest</option>
                    <option value='Smilestones'>Smilestones Guest</option>
                    <option value='Sanisa'>Sanisa Guest</option>
                    <option value='Personal'>Personal Guest</option>
                    <option value='Events-Guest'>Events Guest</option>
                  </select>
                </div>

                <div class="form-group">
                  <label class="col-form-label" for="meals">Select Meals:</label>
                  <div class="d-flex gap-3 align-items-center">
                    <label class="col-form-label">
                      <input type="checkbox" id="breakfast" name="meal" value="breakfast" />
                      Breakfast
                    </label>
                    <label class="col-form-label">
                      <input type="checkbox" id="lunch" name="meal" value="lunch" />
                      Lunch
                    </label>
                    <label class="col-form-label">
                      <input type="checkbox" id="dinner" name="meal" value="dinner" />
                      Dinner
                    </label>
                  </div>
                </div>
                

                <div class="form-group form-submit">
                  <button type="submit" class="btn btn-primary">Submit</button>
                  &nbsp;&nbsp;
                  <a href="#" onclick="getExistingGuestBookings();">Get Existing Guest Bookings</a>
                </div>
              </form>
            </div>

            <div class="frm-head">
              <h2>Manage Guest Food Bookings</h2>
            </div>

            <input type="text" id="tableSearch" class="form-control search-box" placeholder="Search...">
            <div class="table-responsive">
              <table id="bookingsTable" class="table table-striped table-bordered">
                <thead>
                  <tr>
                    <th></th>
                    <th>Date</th>
                    <th>Booked By</th>
                    <th>Mobile No.</th>
                    <th>Department</th>
                    <th>Guest Count</th>
                    <th>Meals</th>
                  </tr>
                </thead>
                <tbody id="bookingsTableBody">
                  <!-- Filled dynamically -->
                </tbody>
              </table>
            </div>

          </div>
        </div>
      </div>
    </div>
  </body>
</html>
