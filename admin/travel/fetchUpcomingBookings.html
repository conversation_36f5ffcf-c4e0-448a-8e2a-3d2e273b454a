<script src="/style/js/roleCheck.js"></script>
<script>
  checkRoleAccess(['travelAdmin', 'superAdmin']); // customize allowed roles per page
</script>

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Upcoming Travel Bookings</title>

    <!-- jQuery -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.2/jquery.min.js"></script>

    <!-- Additional Scripts -->
    <script src="../../style/js/plugin.js"></script>
    <script src="../../style/js/bootstrap-datepicker.min.js"></script>
    <script src="../../style/js/clockpicker.js"></script>
    <script src="../../style/js/custom.js"></script>
    <script src="../../style/js/config.js"></script>
    <script src="../../style/js/formatDate.js"></script>
    <script src="/sessionstorage.js"></script>
    <script src="fetchUpcomingBookings.js" defer></script>
    <script src="../../style/js/tableSortFilter.js" defer></script>
    <script src="https://cdn.sheetjs.com/xlsx-latest/package/dist/xlsx.full.min.js"></script>
    <script src="../../style/js/exportToExcel.js"></script>


    <link rel="stylesheet" href="../../style/css/styles.css" />
    
  </head>

  <body>
    <div class="header">
      <div class="container">
        <div class="logout">
          <a href="javascript:void(0);" onclick="history.back()">Back</a>
          &nbsp; | &nbsp;
          <a href="javascript:void(0);" onclick="goToHome()">Home</a>
          &nbsp; | &nbsp;
          <a href="javascript:void(0);" onclick="logout()">Logout</a>
        </div>
      </div>
    </div>
    
    <div class="middlecontent">
      <div class="container">
        <div class="whitesec">
          <div class="inner-padding">

            <div class="frm-head">
              <h1>Upcoming Travel Bookings</h1>
            </div>

            <div class="form">
              <div id="alert" class="alert" role="alert" style="display:none;"></div>
              <form id="reportForm">
                
                <div class="form-group">
                  <label>Status:</label>
                  <div class="checkbox-group" style="display: flex; flex-wrap: wrap; gap: 1rem;">
                    <label><input type="checkbox" name="status" value="confirmed" /> Confirmed </label>
                    <label><input type="checkbox" name="status" value="proceed for payment" /> Proceed for Payment</label>
                    <label><input type="checkbox" name="status" value="awaiting confirmation" /> Awaiting Confirmation for Payment</label>
                    <label><input type="checkbox" name="status" value="waiting" /> Awaiting Confirmation for Payment (datachef)</label>
                    <label><input type="checkbox" name="status" value="cancelled" /> Self-Cancel </label>
                    <label><input type="checkbox" name="status" value="wrong form cancel" /> Cancelled as wrong form filled</label>
                    <label><input type="checkbox" name="status" value="admin cancelled" /> Admin Cancelled</label>
                    <label><input type="checkbox" name="status" value="seats full cancel" /> Cancelled as all seats are booked</label>
                  </div>
                </div>

                <!-- <div class="form-group" style="display: flex; align-items: center; gap: 2rem;">
                  <label><input type="radio" name="rcOption" id="pickupRC" value="pickup" /> Pickup at RC</label>
                  <label><input type="radio" name="rcOption" id="dropRC" value="drop" /> Drop at RC</label>
                </div> -->
                                

                <div class="form-group">
                  <label for="start_date">*Start Date:</label>
                  <input type="date" id="start_date" name="start_date" class="form-control" required />
                </div>

                <div class="form-group">
                  <label for="end_date">*End Date:</label>
                  <input type="date" id="end_date" name="end_date" class="form-control" required />
                </div>

                <div class="form-group form-submit">
  <div style="display: inline-block;">
    <button type="submit" class="btn btn-primary">Submit</button>
  </div>
  <div id="downloadBtnContainer" style="display: inline-block; margin-left: 10px;"></div>
</div>

              </form>
            </div>

            <div class="form">
  <h3>Summary <span id="selectedDate"></span> </h3>

  <div class="table-responsive">
    <table
      id="summaryBooking"
      class="table table-striped table-bordered"
      style="text-align: center; width: auto;"
    >
      <thead>
        <tr>
          <th>Destination</th>
          <th>Status</th>
          <th>Count</th>
        </tr>
      </thead>
      <tbody>
        <!-- Filled via JS -->
      </tbody>
    </table>
  </div>
</div>

<input type="text" id="tableSearch" class="form-control search-box" placeholder="Search...">
                            <div class="table-responsive">
                <table
                  id="upcomingBookings"
                  class="table table-striped table-bordered"
                >
                  <thead>
  <tr>
    <th>#</th> 
    <th data-key="date">Travel Date</th>
    <th data-key="issuedto">Mumukshu Name</th>
    <th data-key="type">Type (Regular/Full Car)</th>
    <th data-key="pickup_point">Pickup Point</th>
    <th data-key="drop_point">Drop off Point</th>
    <th data-key="arrival_time">Tranin/Flight Time</th>
    <th data-key="leaving_post_adhyayan">Attending Adhyayan</th>
    <th data-key="status">Booking Status</th>
    <th data-key="action">Action</th>
    <th data-key="comments">Mumukshu Comments</th>
    <th data-key="total_people">Total People</th>
    <th data-key="mobno">Mobile No</th>
    <th data-key="amount">Amount</th>
    <th data-key="paymentStatus">Payment Status</th>
    <th data-key="paymentDate">Payment Date</th>
    <th data-key="bookingid">Booking Id</th>
    <th data-key="bookedBy">Booked By</th>
    <th data-key="adminComments">Admin Comments</th>
    <th data-key="luggage">Luggage</th>
    <th data-key="travellingFrom">Travelling From</th>
  </tr>
</thead>
<tbody>
                    <!-- Filled via JS -->
                  </tbody>
                </table>
                
              </div>
            
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Scrollable Modal for Updating Booking Status -->
<div id="updateModal" class="modal" style="
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.6);
  z-index: 9999;
  overflow: auto;
">
  <div class="modal-content" style="
    background: white;
    padding: 2rem;
    margin: 5% auto;
    width: 90%;
    max-width: 600px;
    position: relative;
    border-radius: 10px;
  ">
    <span id="closeModal" style="
      position: absolute;
      top: 10px;
      right: 15px;
      font-size: 24px;
      cursor: pointer;
    ">&times;</span>

    <h2>Update Booking Status</h2>
    <form id="updateBookingForm">
      <div class="form-group">
        <label for="bookingid">Booking ID:</label>
        <input type="text" id="bookingid" name="bookingid" class="form-control" readonly />
      </div>

      <div class="form-group">
        <label for="status">New Status:</label>
        <select id="status" name="status" class="form-control" required>
          <option value="">Select Status</option>
          <option value="confirmed">Confirmed</option>
          <option value="proceed for payment">Proceed for Payment</option>
          <option value="awaiting confirmation">Awaiting Confirmation for Payment</option>
          <option value="wrong form cancel">Cancelled as wrong form filled</option>
          <option value="admin cancelled">Admin Cancelled</option>
          <option value="seats full cancel">Cancelled as all seats are booked</option>
        </select>
      </div>

      <div class="form-group">
        <label for="charges">Charges:</label>
        <input type="number" id="charges" name="charges" class="form-control" />
      </div>

      <div class="form-group">
        <label for="description">Transaction Description:</label>
        <input type="text" id="description" name="description" class="form-control" />
      </div>

      <div class="form-group">
        <label for="adminComments">Admin Comments:</label>
        <input type="text" id="adminComments" name="adminComments" class="form-control" />
      </div>

      <div class="form-group form-submit">
        <button type="submit" class="btn btn-primary">Update Status</button>
        <button type="button" id="cancelUpdate" class="btn btn-secondary">Cancel</button>
      </div>

      <p id="statusMessage" style="margin-top:10px;"></p>
    </form>
  </div>
</div>

  </body>
</html>
