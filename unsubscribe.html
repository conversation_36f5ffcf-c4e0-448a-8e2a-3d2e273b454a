<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" href="./assets/images/logo.png" />
    <title>V<PERSON><PERSON><PERSON><PERSON> - Unsubscribe</title>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <style>
      /* Base styles and reset */
      :root {
        --primary: #f1ac09;
        --primary-light: #ffbd30;
        --primary-dark: #d99600;
        --primary-bg: rgba(241, 172, 9, 0.05);
        --neutral-900: #171717;
        --neutral-800: #262626;
        --neutral-700: #404040;
        --neutral-600: #525252;
        --neutral-500: #737373;
        --neutral-400: #a3a3a3;
        --neutral-300: #d4d4d4;
        --neutral-200: #e5e5e5;
        --neutral-100: #f5f5f5;
        --neutral-50: #fafafa;
        --white: #ffffff;
        --success: #22c55e;
        --radius-sm: 4px;
        --radius: 8px;
        --radius-md: 12px;
        --radius-lg: 16px;
        --radius-xl: 24px;
        --radius-full: 9999px;
        --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
        --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
        --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
          0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
          0 4px 6px -2px rgba(0, 0, 0, 0.05);
        --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
          0 10px 10px -5px rgba(0, 0, 0, 0.04);
        --shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        --header-height: 72px;
      }

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI',
          Roboto, sans-serif;
        color: var(--neutral-800);
        line-height: 1.6;
        background-color: var(--white);
        font-size: 16px;
        overflow-x: hidden;
      }

      /* Typography */
      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        line-height: 1.2;
        font-weight: 700;
        color: var(--neutral-900);
      }

      /* Header */
      .header {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: var(--header-height);
        backdrop-filter: blur(12px);
        -webkit-backdrop-filter: blur(12px);
        background-color: rgba(255, 255, 255, 0.75);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        z-index: 100;
        transition: all 0.3s ease;
      }

      .logo img {
        height: 40px;
        width: auto;
      }

      /* Layout */
      .container {
        width: 100%;
        max-width: 1280px;
        margin: 0 auto;
        padding: 0 24px;
      }

      /* Hero Section */
      .hero-section {
        min-height: 100vh;
        display: flex;
        align-items: center;
        padding: calc(var(--header-height) + 60px) 0 100px;
        position: relative;
        overflow: hidden;
        background: linear-gradient(
          170deg,
          var(--white) 0%,
          var(--neutral-50) 100%
        );
      }

      .hero-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 64px;
        align-items: center;
      }

      .hero-text h1 {
        font-size: clamp(2.5rem, 5vw, 4.5rem);
        line-height: 1.1;
        margin-bottom: 32px;
        color: var(--neutral-900);
        position: relative;
        font-weight: 800;
        letter-spacing: -0.02em;
      }

      .hero-text p {
        font-size: clamp(1.125rem, 2vw, 1.25rem);
        color: var(--neutral-600);
        margin-bottom: 32px;
        max-width: 500px;
      }


      /* Footer */
      .footer {
        background-color: var(--neutral-900);
        color: var(--neutral-300);
        padding: 80px 0 40px;
      }

      .footer-bottom {
        padding-top: 32px;
        border-top: 1px solid rgba(255, 255, 255, 0.05);
        text-align: center;
      }

      .footer-bottom p {
        color: var(--neutral-500);
        font-size: 0.875rem;
      }

    </style>
  </head>
  <body>
    <!-- Header -->
    <header class="header" id="header">
      <div class="container">
        <div class="header-inner">
          <a href="#" class="logo">
            <img src="./assets/images/logo.png" alt="Vitraag Vigyaan Logo" />
          </a>
        </div>
      </div>
    </header>

    <!-- Hero Section -->
    <section class="hero-section">
      <div class="container">
        <div class="hero-content">
          <div class="hero-text">
            <h1>
              Unsubscribe
            </h1>
            <p>
              You have successfully unsubscribed from all email notifications.
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
      <div class="container">
        <div class="footer-bottom">
          <p>&copy; 2025 Vitraag Vigyaan. All rights reserved.</p>
        </div>
      </div>
    </footer>
  </body>
</html>
